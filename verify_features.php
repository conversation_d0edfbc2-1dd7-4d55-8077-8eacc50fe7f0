<?php
/**
 * WIDDX Theme Feature Verification Script
 * 
 * This script verifies that all WIDDX theme features are properly installed
 * and configured for FOSSBilling.
 */

echo "=== WIDDX Theme Feature Verification ===\n\n";

$errors = [];
$warnings = [];
$success = [];

// Check theme directory structure
$requiredDirs = [
    'themes/widdx',
    'themes/widdx/assets',
    'themes/widdx/assets/css',
    'themes/widdx/assets/js',
    'themes/widdx/assets/img',
    'themes/widdx/build',
    'themes/widdx/build/css',
    'themes/widdx/build/js',
    'themes/widdx/config',
    'themes/widdx/html'
];

echo "1. Checking directory structure...\n";
foreach ($requiredDirs as $dir) {
    if (is_dir($dir)) {
        $success[] = "✓ Directory exists: $dir";
    } else {
        $errors[] = "✗ Missing directory: $dir";
    }
}

// Check required files
$requiredFiles = [
    'themes/widdx/manifest.json',
    'themes/widdx/assets/css/widdx.css',
    'themes/widdx/assets/js/widdx.js',
    'themes/widdx/assets/widdx.js',
    'themes/widdx/build/css/widdx-bundle.css',
    'themes/widdx/build/js/widdx-bundle.js',
    'themes/widdx/build/entrypoints.json',
    'themes/widdx/config/settings.html.twig',
    'themes/widdx/config/settings_data.json',
    'themes/widdx/html/layout_default.html.twig',
    'themes/widdx/html/layout_public.html.twig',
    'themes/widdx/html/macro_functions.html.twig',
    'themes/widdx/html/partial_menu.html.twig',
    'themes/widdx/html/mobile_menu.html.twig',
    'themes/widdx/html/partial_message.html.twig',
    'themes/widdx/html/partial_pagination.html.twig',
    'themes/widdx/html/partial_pending_messages.html.twig',
    'themes/widdx/html/partial_pricing.html.twig',
    'themes/widdx/html/error.html.twig',
    'themes/widdx/package.json',
    'themes/widdx/webpack.config.js',
    'themes/widdx/postcss.config.js'
];

echo "\n2. Checking required files...\n";
foreach ($requiredFiles as $file) {
    if (file_exists($file)) {
        $success[] = "✓ File exists: $file";
    } else {
        $errors[] = "✗ Missing file: $file";
    }
}

// Check manifest.json content
echo "\n3. Checking manifest.json...\n";
if (file_exists('themes/widdx/manifest.json')) {
    $manifest = json_decode(file_get_contents('themes/widdx/manifest.json'), true);
    if ($manifest) {
        if (isset($manifest['name']) && $manifest['name'] === 'WIDDX Modern Theme') {
            $success[] = "✓ Manifest name is correct";
        } else {
            $warnings[] = "⚠ Manifest name might be incorrect";
        }
        
        if (isset($manifest['markdown_attributes'])) {
            $success[] = "✓ Manifest has markdown_attributes";
        } else {
            $errors[] = "✗ Manifest missing markdown_attributes";
        }
    } else {
        $errors[] = "✗ Invalid JSON in manifest.json";
    }
}

// Check entrypoints.json
echo "\n4. Checking entrypoints.json...\n";
if (file_exists('themes/widdx/build/entrypoints.json')) {
    $entrypoints = json_decode(file_get_contents('themes/widdx/build/entrypoints.json'), true);
    if ($entrypoints && isset($entrypoints['entrypoints']['widdx'])) {
        $success[] = "✓ Entrypoints.json is valid";
    } else {
        $errors[] = "✗ Invalid entrypoints.json structure";
    }
}

// Check CSS file size and content
echo "\n5. Checking CSS assets...\n";
if (file_exists('themes/widdx/assets/css/widdx.css')) {
    $cssSize = filesize('themes/widdx/assets/css/widdx.css');
    if ($cssSize > 10000) { // Should be substantial
        $success[] = "✓ CSS file has substantial content ($cssSize bytes)";
    } else {
        $warnings[] = "⚠ CSS file seems small ($cssSize bytes)";
    }
    
    $cssContent = file_get_contents('themes/widdx/assets/css/widdx.css');
    $features = [
        'CSS Variables' => '--widdx-primary',
        'Navbar Styles' => '.navbar',
        'Button Styles' => '.btn',
        'Card Styles' => '.card',
        'Alert Styles' => '.alert',
        'Badge Styles' => '.badge',
        'Table Styles' => '.table',
        'Form Styles' => '.form-control',
        'Tooltip Styles' => '.widdx-tooltip',
        'Loading Styles' => '.loading'
    ];
    
    foreach ($features as $feature => $selector) {
        if (strpos($cssContent, $selector) !== false) {
            $success[] = "✓ CSS includes $feature";
        } else {
            $warnings[] = "⚠ CSS missing $feature";
        }
    }
}

// Check JavaScript functionality
echo "\n6. Checking JavaScript assets...\n";
if (file_exists('themes/widdx/assets/js/widdx.js')) {
    $jsContent = file_get_contents('themes/widdx/assets/js/widdx.js');
    $jsFunctions = [
        'Theme Initialization' => 'initializeTheme',
        'FOSSBilling Integration' => 'initializeFOSSBilling',
        'API Forms' => 'initializeAPIForms',
        'API Links' => 'initializeAPILinks',
        'Flash Messages' => 'flashMessage',
        'Dropdown Management' => 'initializeDropdowns',
        'Navigation' => 'initializeNavigation',
        'Form Handling' => 'initializeForms'
    ];
    
    foreach ($jsFunctions as $feature => $func) {
        if (strpos($jsContent, $func) !== false) {
            $success[] = "✓ JavaScript includes $feature";
        } else {
            $warnings[] = "⚠ JavaScript missing $feature";
        }
    }
}

// Check template files for FOSSBilling compatibility
echo "\n7. Checking template compatibility...\n";
$templates = [
    'themes/widdx/html/layout_default.html.twig' => [
        'encore_entry_exists' => 'Webpack Encore support',
        'asset_url' => 'Asset URL filter',
        'client.profile_get' => 'Profile handling'
    ],
    'themes/widdx/html/partial_menu.html.twig' => [
        'settings.side_menu' => 'Settings-based menu',
        'guest.extension_is_on' => 'Extension checks'
    ]
];

foreach ($templates as $template => $checks) {
    if (file_exists($template)) {
        $content = file_get_contents($template);
        foreach ($checks as $check => $description) {
            if (strpos($content, $check) !== false) {
                $success[] = "✓ $template has $description";
            } else {
                $warnings[] = "⚠ $template missing $description";
            }
        }
    }
}

// Summary
echo "\n=== VERIFICATION SUMMARY ===\n";
echo "✓ Success: " . count($success) . " items\n";
echo "⚠ Warnings: " . count($warnings) . " items\n";
echo "✗ Errors: " . count($errors) . " items\n\n";

if (!empty($errors)) {
    echo "ERRORS:\n";
    foreach ($errors as $error) {
        echo "$error\n";
    }
    echo "\n";
}

if (!empty($warnings)) {
    echo "WARNINGS:\n";
    foreach ($warnings as $warning) {
        echo "$warning\n";
    }
    echo "\n";
}

if (empty($errors)) {
    echo "🎉 WIDDX Theme verification completed successfully!\n";
    echo "All essential features are present and properly configured.\n\n";
    echo "Next steps:\n";
    echo "1. Activate the theme in FOSSBilling admin panel\n";
    echo "2. Configure theme settings as needed\n";
    echo "3. Test all functionality in your FOSSBilling installation\n";
} else {
    echo "❌ Theme verification failed. Please fix the errors above.\n";
}

echo "\n=== END VERIFICATION ===\n";
?>
